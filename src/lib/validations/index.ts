import { z } from "zod"

// User validation schemas
export const userProfileSchema = z.object({
  full_name: z.string().min(1, "Full name is required"),
  role: z.enum(["admin", "manager", "employee"]),
  avatar_url: z.string().url().optional().or(z.literal("")),
})

// Client validation schemas
export const addressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
})

export const clientSchema = z.object({
  name: z.string().min(1, "Client name is required"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")).transform(val => val === "" ? null : val),
  phone: z.string().optional().transform(val => val === "" ? null : val),
  company: z.string().optional().transform(val => val === "" ? null : val),
  address: addressSchema.optional(),
  status: z.enum(["lead", "active", "inactive", "archived"]).default("lead"),
  lead_source: z.string().optional().transform(val => val === "" ? null : val),
  assigned_to: z.string().uuid().optional(),
})

export const updateClientSchema = clientSchema.partial()

// Project validation schemas
export const projectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  client_id: z.string().uuid("Invalid client ID"),
  status: z.enum(["planning", "in_progress", "review", "completed", "cancelled"]).default("planning"),
  service_type: z.enum([
    "jasa_pembuatan_website",
    "jasa_redesign_website",
    "wordpress_hosting",
    "jasa_maintenance_website",
    "jasa_perbaikan_website",
    "jasa_remove_malware",
    "jasa_migrasi_hosting",
    "jasa_migrasi_website_ke_astro",
    "jasa_konversi_wordpress_ke_blocks",
    "jasa_audit_optimasi_seo_onpage"
  ]).optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  budget: z.number().positive("Budget must be positive").optional(),
  assigned_team: z.array(z.string().uuid()).optional(),
  created_by: z.string().uuid(),
})

// Client-side form validation schema (excludes server-side fields)
export const projectFormSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  client_id: z.string().min(1, "Please select a client").uuid("Invalid client ID"),
  status: z.enum(["planning", "in_progress", "review", "completed", "cancelled"]).default("planning"),
  service_type: z.enum([
    "jasa_pembuatan_website",
    "jasa_redesign_website",
    "wordpress_hosting",
    "jasa_maintenance_website",
    "jasa_perbaikan_website",
    "jasa_remove_malware",
    "jasa_migrasi_hosting",
    "jasa_migrasi_website_ke_astro",
    "jasa_konversi_wordpress_ke_blocks",
    "jasa_audit_optimasi_seo_onpage"
  ]).optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  budget: z.number().positive("Budget must be positive").optional(),
  assigned_team: z.array(z.string().uuid()).optional(),
})

export const updateProjectSchema = projectSchema.partial().omit({ created_by: true })

// Invoice validation schemas
export const invoiceItemSchema = z.object({
  id: z.string(),
  description: z.string().min(1, "Description is required"),
  quantity: z.number().min(0, "Quantity cannot be negative"),
  rate: z.number().min(0, "Rate cannot be negative"),
  amount: z.number().min(0, "Amount cannot be negative"),
})

export const invoiceSchema = z.object({
  client_id: z.string().uuid("Invalid client ID"),
  project_id: z.string().uuid().optional(),
  amount: z.number().positive("Amount must be positive"),
  tax_amount: z.number().min(0, "Tax amount cannot be negative").default(0),
  discount_amount: z.number().min(0, "Discount amount cannot be negative").default(0),
  discount_type: z.enum(["amount", "percentage"]).default("amount"),
  total_amount: z.number().positive("Total amount must be positive"),
  currency: z.enum(["USD", "IDR"]).default("IDR"),
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled"]).default("draft"),
  invoice_date: z.string().optional(),
  due_date: z.string().optional(),
  paid_date: z.string().optional(),
  items: z.array(invoiceItemSchema).optional(),
  notes: z.string().optional(),
  created_by: z.string().uuid(),
  milestone_type: z.enum(["dp", "progress", "final", "standard"]).default("standard"),
  parent_invoice_id: z.string().uuid().optional(),
  milestone_percentage: z.number().min(0).max(100).optional(),
  sequence_number: z.number().int().positive().default(1),
})

// Client-side form validation schema (excludes server-side fields)
export const invoiceFormSchema = z.object({
  client_id: z.string().min(1, "Please select a client").uuid("Invalid client ID"),
  project_id: z.string().uuid().optional().or(z.literal("")).or(z.literal("none")),
  amount: z.number().min(0, "Amount cannot be negative"),
  tax_amount: z.number().min(0, "Tax amount cannot be negative"),
  discount_amount: z.number().min(0, "Discount amount cannot be negative").default(0),
  discount_type: z.enum(["amount", "percentage"]).default("amount"),
  total_amount: z.number().min(0, "Total amount cannot be negative"),
  currency: z.enum(["USD", "IDR"]),
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled"]),
  invoice_date: z.string().optional(),
  due_date: z.string().optional(),
  paid_date: z.string().optional(),
  items: z.array(invoiceItemSchema).min(1, "At least one item is required"),
  notes: z.string().optional(),
  milestone_type: z.enum(["dp", "progress", "final", "standard"]).default("standard"),
  parent_invoice_id: z.string().uuid().optional().or(z.literal("")).or(z.literal(null)),
  milestone_percentage: z.number().min(0).max(100).optional(),
  sequence_number: z.number().int().positive().default(1),
}).refine((data) => {
  // Validate discount percentage is not more than 100%
  if (data.discount_type === "percentage" && data.discount_amount > 100) {
    return false
  }
  return true
}, {
  message: "Discount percentage cannot exceed 100%",
  path: ["discount_amount"]
})

// Payment milestone creation schema
export const paymentMilestoneSchema = z.object({
  type: z.enum(["dp", "progress", "final"]),
  percentage: z.number().min(1).max(100),
  description: z.string().min(1, "Description is required"),
  due_date: z.string().optional(),
})

export const createMilestonesSchema = z.object({
  project_id: z.string().uuid("Invalid project ID"),
  milestones: z.array(paymentMilestoneSchema).min(1, "At least one milestone is required"),
}).refine((data) => {
  const totalPercentage = data.milestones.reduce((sum, milestone) => sum + milestone.percentage, 0)
  return totalPercentage === 100
}, {
  message: "Total milestone percentages must equal 100%",
  path: ["milestones"]
})

export const updateInvoiceSchema = invoiceSchema.partial().omit({ created_by: true })

// Authentication validation schemas
export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

export const registerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string(),
  full_name: z.string().min(1, "Full name is required"),
  role: z.enum(["admin", "manager", "employee"]).default("employee"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// Search and filter schemas
export const clientFilterSchema = z.object({
  status: z.enum(["lead", "active", "inactive", "archived"]).optional(),
  assigned_to: z.string().uuid().optional(),
  search: z.string().optional(),
})

export const projectFilterSchema = z.object({
  status: z.enum(["planning", "in_progress", "review", "completed", "cancelled"]).optional(),
  client_id: z.string().uuid().optional(),
  created_by: z.string().uuid().optional(),
  search: z.string().optional(),
})

export const invoiceFilterSchema = z.object({
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled"]).optional(),
  client_id: z.string().uuid().optional(),
  project_id: z.string().uuid().optional(),
  search: z.string().optional(),
})

// Export types
export type UserProfileFormData = z.infer<typeof userProfileSchema>
export type ClientFormData = z.infer<typeof clientSchema>
export type UpdateClientFormData = z.infer<typeof updateClientSchema>
export type ProjectFormData = z.infer<typeof projectFormSchema>
export type UpdateProjectFormData = z.infer<typeof updateProjectSchema>
export type InvoiceItemFormData = z.infer<typeof invoiceItemSchema>
export type InvoiceFormData = z.infer<typeof invoiceFormSchema>
export type UpdateInvoiceFormData = z.infer<typeof updateInvoiceSchema>
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type ClientFilterData = z.infer<typeof clientFilterSchema>
export type ProjectFilterData = z.infer<typeof projectFilterSchema>
export type InvoiceFilterData = z.infer<typeof invoiceFilterSchema>
export type PaymentMilestoneData = z.infer<typeof paymentMilestoneSchema>
export type CreateMilestonesData = z.infer<typeof createMilestonesSchema>
